# JSONAPIParser 使用示例

## 完整使用示例

以下是一个完整的使用示例，展示如何配置和使用新的JSONAPIParser：

### 1. 配置示例

假设我们要爬取一个电商网站的商品API，返回的JSON格式如下：

```json
{
  "status": "success",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "iPhone 15",
        "price": 5999,
        "category": "手机",
        "brand": "Apple"
      },
      {
        "id": 2,
        "name": "MacBook Pro",
        "price": 12999,
        "category": "电脑",
        "brand": "Apple"
      }
    ],
    "pagination": {
      "page": 1,
      "total": 100
    }
  }
}
```

### 2. 解析配置

```json
{
  "product_list": [
    {
      "parse_mode": "raw",
      "store_engine": "mongodb",
      "collection": "raw_product_pages"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "products",
      "jsonpath": "$.data.products[*]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mysql",
      "tablename": "product_stats",
      "jsonpath": "$.data.pagination"
    }
  ]
}
```

### 3. 处理结果

使用上述配置，JSONAPIParser会：

1. **Raw模式 + MongoDB**: 将完整的JSON响应保存到`raw_product_pages`集合
   ```json
   {
     "data": "{\"status\":\"success\",\"data\":{...}}",
     "task_id": "task-123",
     "create_time": "2024-01-01T12:00:00Z"
   }
   ```

2. **JSONPath模式 + MongoDB**: 提取商品数组并保存到`products`集合
   ```json
   // 第一条记录
   {
     "id": 1,
     "name": "iPhone 15",
     "price": 5999,
     "category": "手机",
     "brand": "Apple"
   }
   // 第二条记录
   {
     "id": 2,
     "name": "MacBook Pro",
     "price": 12999,
     "category": "电脑",
     "brand": "Apple"
   }
   ```

3. **JSONPath模式 + MySQL**: 提取分页信息并保存到`product_stats`表
   ```json
   {
     "data": "{\"page\":1,\"total\":100}"
   }
   ```

### 4. 高级JSONPath示例

```json
{
  "advanced_parsing": [
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "apple_products",
      "jsonpath": "$.data.products[?(@.brand == 'Apple')]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "expensive_products",
      "jsonpath": "$.data.products[?(@.price > 10000)]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "product_names",
      "jsonpath": "$.data.products[*].name"
    }
  ]
}
```

### 5. 错误处理示例

```json
{
  "invalid_config": [
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      // 错误：缺少collection配置
      "jsonpath": "$.data.products[*]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mysql",
      // 错误：缺少tablename配置
      "jsonpath": "$.data.products[*]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "products"
      // 错误：缺少jsonpath配置
    }
  ]
}
```

### 6. 环境变量配置

确保设置以下环境变量：

```bash
# MongoDB配置
export MONGO_URI="mongodb://localhost:27017"
export MONGO_DATABASE="crawler_db"
export MONGO_COLLECTION="default_collection"

# MySQL配置
export MYSQL_DSN="user:password@tcp(localhost:3306)/crawler_db"
export MYSQL_TABLE="default_table"
```

### 7. 代码使用示例

```go
// 创建解析器
parser, err := parser.NewJSONAPIParser()
if err != nil {
    log.Fatal(err)
}
defer parser.Close(context.Background())

// 准备数据
content := []byte(`{"status":"success","data":{"products":[...]}}`)
urlItem := &models.URLQueueItem{
    TaskID:   "task-123",
    PageType: "product_list",
    URL:      "https://api.example.com/products",
}
parseConfig := `{"product_list":[...]}`

// 执行解析
result, err := parser.Parse(context.Background(), content, urlItem, parseConfig)
if err != nil {
    log.Printf("解析失败: %v", err)
    return
}

log.Printf("解析完成，处理了 %d 个URL项", len(result.Items))
```

## 注意事项

1. **数据库连接**: 确保MongoDB和MySQL连接配置正确
2. **表结构**: MySQL表需要预先创建，包含相应的列
3. **JSONPath语法**: 使用oliveagle/jsonpath库的完整语法
4. **错误处理**: 配置错误会导致解析失败，需要检查日志
5. **性能考虑**: 大量数据时考虑批量插入的性能影响
