# JSONAPIParser 使用说明

## 概述

JSONAPIParser 是一个支持多种解析模式和存储引擎的JSON解析器，可以根据配置将JSON数据保存到MongoDB或MySQL中。

## 功能特性

- **两种解析模式**：
  - `raw`: 原始模式，几乎不解析直接保存content
  - `jsonpath`: JSONPath模式，使用JSONPath表达式提取特定数据

- **两种存储引擎**：
  - `mongodb`: 保存到MongoDB
  - `mysql`: 保存到MySQL

## 配置格式

Parse函数的parseConfig参数是一个JSON字符串，格式如下：

```json
{
   "页面类型1": [ 
          {
            "parse_mode": "raw|jsonpath",
            "store_engine": "mongodb|mysql",
            "collection": "MongoDB集合名",
            "tablename": "MySQL表名",
            "jsonpath": "JSONPath表达式"
          }
    ],
   "页面类型2": [
          // 更多解析规则...
    ]
}
```

### 配置字段说明

- `parse_mode`: 解析模式
  - `raw`: 原始模式
  - `jsonpath`: JSONPath模式
- `store_engine`: 存储引擎
  - `mongodb`: MongoDB存储
  - `mysql`: MySQL存储
- `collection`: MongoDB集合名（当store_engine为mongodb时使用）
- `tablename`: MySQL表名（当store_engine为mysql时使用）
- `jsonpath`: JSONPath表达式（当parse_mode为jsonpath时必须配置）

## 使用示例

### 1. Raw模式 + MongoDB存储

```json
{
  "default": [
    {
      "parse_mode": "raw",
      "store_engine": "mongodb",
      "collection": "raw_data"
    }
  ]
}
```

保存格式：
```json
{
  "data": "原始content内容",
  "task_id": "任务ID",
  "create_time": "2024-01-01T12:00:00Z"
}
```

### 2. Raw模式 + MySQL存储

```json
{
  "default": [
    {
      "parse_mode": "raw",
      "store_engine": "mysql",
      "tablename": "raw_data"
    }
  ]
}
```

MySQL表结构：
```sql
CREATE TABLE raw_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  data TEXT,
  task_id VARCHAR(255),
  create_time TIMESTAMP
);
```

### 3. JSONPath模式 + MongoDB存储

```json
{
  "book_list": [
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "books",
      "jsonpath": "$.store.book[*]"
    }
  ]
}
```

假设原始JSON：
```json
{
  "store": {
    "book": [
      {"name": "Go语言编程", "author": "张三", "price": 59.9},
      {"name": "Python实战", "author": "李四", "price": 49.9}
    ]
  }
}
```

MongoDB中会插入两条记录：
```json
{"name": "Go语言编程", "author": "张三", "price": 59.9}
{"name": "Python实战", "author": "李四", "price": 49.9}
```

### 4. JSONPath模式 + MySQL存储

```json
{
  "book_list": [
    {
      "parse_mode": "jsonpath",
      "store_engine": "mysql",
      "tablename": "books",
      "jsonpath": "$.store.book[*]"
    }
  ]
}
```

MySQL表结构：
```sql
CREATE TABLE books (
  id INT AUTO_INCREMENT PRIMARY KEY,
  data JSON
);
```

### 5. 多规则配置示例

```json
{
  "product_page": [
    {
      "parse_mode": "raw",
      "store_engine": "mongodb",
      "collection": "raw_products"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "products",
      "jsonpath": "$.products[*]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mysql",
      "tablename": "product_categories",
      "jsonpath": "$.categories[*]"
    }
  ]
}
```

## JSONPath语法支持

本实现使用 `github.com/oliveagle/jsonpath` 库，支持完整的JSONPath语法：

### 基本语法
- `$`: 根对象
- `$.field`: 获取根对象的field字段
- `$.field1.field2`: 嵌套字段访问
- `$.field[*]`: 获取数组的所有元素
- `$.field[0]`: 获取数组的第一个元素
- `$.field[0,1]`: 获取数组的第0和第1个元素
- `$.field[-1]`: 获取数组的最后一个元素

### 高级语法
- `$..field`: 递归搜索所有field字段
- `$.field[?(@.price > 10)]`: 条件过滤
- `$.field[*].name`: 获取数组中所有对象的name字段
- `$.field.length()`: 获取数组长度

### 示例
- `$.store.book[*]`: 获取store对象下book数组的所有元素
- `$.store.book[0].name`: 获取第一本书的名称
- `$.store.book[*].name`: 获取所有书的名称
- `$.store.book[?(@.price > 50)]`: 获取价格大于50的书
- `$..author`: 递归获取所有author字段

## 错误处理

- JSONPath模式下，如果使用MongoDB存储，必须配置`collection`字段
- JSONPath模式下，如果使用MySQL存储，必须配置`tablename`字段
- JSONPath模式下，必须配置`jsonpath`字段
- 如果JSONPath表达式无法匹配数据，会返回错误

## 默认值

- 如果Raw模式下未配置collection，使用JSONAPIParser的默认mongoCollection
- 如果Raw模式下未配置tablename，使用JSONAPIParser的默认mysqlTablename

## 注意事项

1. MySQL存储时，假设表有一个`data`列用于存储JSON数据
2. 实际使用时可能需要根据具体的表结构调整insertJSONObjectToMySQL方法
3. 确保MongoDB和MySQL连接配置正确
4. JSONPath解析失败时会返回错误，不会跳过
