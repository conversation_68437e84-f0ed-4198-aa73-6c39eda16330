package parser

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	_ "github.com/go-sql-driver/mysql"
	"github.com/oliveagle/jsonpath"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"golden_crawler/internal/models"
)

// ParseRule 定义解析规则结构体
type ParseRule struct {
	ParseMode   string `json:"parse_mode"`   // raw 或 jsonpath
	StoreEngine string `json:"store_engine"` // mongodb 或 mysql
	Collection  string `json:"collection"`   // MongoDB collection名称
	TableName   string `json:"tablename"`    // MySQL表名
	JSONPath    string `json:"jsonpath"`     // JSONPath表达式
}

// ParseConfig 定义解析配置结构体
type ParseConfig map[string][]ParseRule

// JSONAPIParser 实现用于解析JSON API的解析器
type JSONAPIParser struct {
	mongoClient     *mongo.Client
	mongoCollection string // 默认MongoDB collection
	mysqlDB         *sql.DB
	mysqlTablename  string // 默认MySQL表名
}

// NewJSONAPIParser 创建新的JSON API解析器实例
func NewJSONAPIParser() (*JSONAPIParser, error) {
	// 创建Dapr客户端
	daprClient, err := dapr.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}
	defer daprClient.Close()

	// 从环境变量获取MongoDB连接参数
	mongoURI := os.Getenv("MONGO_URI")
	databaseName := os.Getenv("MONGO_DATABASE")
	collectionName := os.Getenv("MONGO_COLLECTION")

	// 如果环境变量中没有，则从配置存储获取MongoDB连接参数
	if mongoURI == "" {
		mongoURIConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_uri")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB URI配置失败: %w", err)
		}
		if mongoURIConfig == nil {
			return nil, fmt.Errorf("MongoDB URI配置为空")
		}
		mongoURI = mongoURIConfig.Value
	}

	if databaseName == "" {
		databaseConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_database")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB数据库名称配置失败: %w", err)
		}
		if databaseConfig == nil {
			return nil, fmt.Errorf("MongoDB数据库名称配置为空")
		}
		databaseName = databaseConfig.Value
	}

	// 检查是否设置了集合名称
	if collectionName == "" {
		collectionConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_collection")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB集合名称配置失败: %w", err)
		}
		if collectionConfig == nil {
			return nil, fmt.Errorf("MongoDB集合名称配置为空")
		}
		collectionName = collectionConfig.Value
	}

	// 创建MongoDB客户端
	clientOptions := options.Client().ApplyURI(mongoURI)
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return nil, fmt.Errorf("连接MongoDB失败: %w", err)
	}

	// 检查连接
	err = client.Ping(context.Background(), nil)
	if err != nil {
		return nil, fmt.Errorf("MongoDB连接测试失败: %w", err)
	}

	// 获取MySQL连接参数
	mysqlTablename := os.Getenv("MYSQL_TABLE")
	if mysqlTablename == "" {
		mysqlTableConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mysql_table")
		if err == nil && mysqlTableConfig != nil {
			mysqlTablename = mysqlTableConfig.Value
		}
		if mysqlTablename == "" {
			mysqlTablename = "parsed_data" // 默认表名
		}
	}

	// 创建MySQL连接
	var mysqlDB *sql.DB
	mysqlDSN := os.Getenv("MYSQL_DSN")
	if mysqlDSN == "" {
		// 尝试从配置获取MySQL连接信息
		mysqlDSNConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mysql_dsn")
		if err == nil && mysqlDSNConfig != nil {
			mysqlDSN = mysqlDSNConfig.Value
		}
	}

	if mysqlDSN != "" {
		mysqlDB, err = sql.Open("mysql", mysqlDSN)
		if err != nil {
			return nil, fmt.Errorf("连接MySQL失败: %w", err)
		}

		// 测试连接
		if err := mysqlDB.Ping(); err != nil {
			return nil, fmt.Errorf("MySQL连接测试失败: %w", err)
		}
	}

	parser := &JSONAPIParser{
		mongoClient:     client,
		mongoCollection: collectionName,
		mysqlDB:         mysqlDB,
		mysqlTablename:  mysqlTablename,
	}

	// 存储数据库名称供后续使用
	if databaseName != "" {
		os.Setenv("MONGO_DATABASE_NAME", databaseName)
	}

	return parser, nil
}

// Parse 实现Parser接口的Parse方法
func (p *JSONAPIParser) Parse(ctx context.Context, content []byte, urlItem *models.URLQueueItem, parseConfigStr string) (*ParseResult, error) {
	// 解析配置
	var config ParseConfig
	if err := json.Unmarshal([]byte(parseConfigStr), &config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 获取页面类型对应的解析规则
	rules, exists := config[urlItem.PageType]
	if !exists {
		return nil, fmt.Errorf("未找到页面类型 %s 的解析规则", urlItem.PageType)
	}

	// 检查JSON是否合法
	var jsonData interface{}
	if err := json.Unmarshal(content, &jsonData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}

	// 处理每个解析规则
	for _, rule := range rules {
		if err := p.processRule(ctx, content, jsonData, urlItem, rule); err != nil {
			return nil, fmt.Errorf("处理解析规则失败: %w", err)
		}
	}

	// 这个解析器不会生成新的URL队列项
	return &ParseResult{
		Items: []*models.URLQueueItem{},
		Data:  jsonData,
	}, nil
}

// processRule 处理单个解析规则
func (p *JSONAPIParser) processRule(ctx context.Context, content []byte, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	switch rule.ParseMode {
	case "raw":
		return p.processRawMode(ctx, content, urlItem, rule)
	case "jsonpath":
		return p.processJSONPathMode(ctx, jsonData, urlItem, rule)
	default:
		return fmt.Errorf("不支持的解析模式: %s", rule.ParseMode)
	}
}

// processRawMode 处理raw解析模式
func (p *JSONAPIParser) processRawMode(ctx context.Context, content []byte, urlItem *models.URLQueueItem, rule ParseRule) error {
	switch rule.StoreEngine {
	case "mongodb":
		return p.saveRawToMongoDB(ctx, content, urlItem, rule)
	case "mysql":
		return p.saveRawToMySQL(ctx, content, urlItem, rule)
	default:
		return fmt.Errorf("不支持的存储引擎: %s", rule.StoreEngine)
	}
}

// processJSONPathMode 处理jsonpath解析模式
func (p *JSONAPIParser) processJSONPathMode(ctx context.Context, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	if rule.JSONPath == "" {
		return fmt.Errorf("jsonpath模式下必须配置jsonpath字段")
	}

	switch rule.StoreEngine {
	case "mongodb":
		if rule.Collection == "" {
			return fmt.Errorf("jsonpath模式下使用mongodb存储必须配置collection字段")
		}
		return p.saveJSONPathToMongoDB(ctx, jsonData, urlItem, rule)
	case "mysql":
		if rule.TableName == "" {
			return fmt.Errorf("jsonpath模式下使用mysql存储必须配置tablename字段")
		}
		return p.saveJSONPathToMySQL(ctx, jsonData, urlItem, rule)
	default:
		return fmt.Errorf("不支持的存储引擎: %s", rule.StoreEngine)
	}
}

// saveRawToMongoDB 保存raw数据到MongoDB
func (p *JSONAPIParser) saveRawToMongoDB(ctx context.Context, content []byte, urlItem *models.URLQueueItem, rule ParseRule) error {
	// 确定collection名称
	collectionName := rule.Collection
	if collectionName == "" {
		collectionName = p.mongoCollection
	}

	// 获取collection
	collection := p.mongoClient.Database(os.Getenv("MONGO_DATABASE")).Collection(collectionName)

	// 保存数据
	_, err := collection.InsertOne(ctx, map[string]interface{}{
		"data":        string(content),
		"task_id":     urlItem.TaskID,
		"create_time": time.Now(),
	})

	if err != nil {
		return fmt.Errorf("保存raw数据到MongoDB失败: %w", err)
	}

	return nil
}

// saveRawToMySQL 保存raw数据到MySQL
func (p *JSONAPIParser) saveRawToMySQL(ctx context.Context, content []byte, urlItem *models.URLQueueItem, rule ParseRule) error {
	if p.mysqlDB == nil {
		return fmt.Errorf("MySQL连接未初始化")
	}

	// 确定表名
	tableName := rule.TableName
	if tableName == "" {
		tableName = p.mysqlTablename
	}

	// 插入数据
	query := fmt.Sprintf("INSERT INTO %s (data, task_id, create_time) VALUES (?, ?, ?)", tableName)
	_, err := p.mysqlDB.ExecContext(ctx, query, string(content), urlItem.TaskID, time.Now())

	if err != nil {
		return fmt.Errorf("保存raw数据到MySQL失败: %w", err)
	}

	return nil
}

// saveJSONPathToMongoDB 保存jsonpath解析结果到MongoDB
func (p *JSONAPIParser) saveJSONPathToMongoDB(ctx context.Context, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	// 使用oliveagle/jsonpath提取数据
	result, err := jsonpath.JsonPathLookup(jsonData, rule.JSONPath)
	if err != nil {
		return fmt.Errorf("jsonpath解析失败: %w", err)
	}

	// 获取数据库名称
	databaseName := os.Getenv("MONGO_DATABASE_NAME")
	if databaseName == "" {
		databaseName = os.Getenv("MONGO_DATABASE")
		if databaseName == "" {
			databaseName = "default_db" // 默认数据库名
		}
	}

	// 获取collection
	collection := p.mongoClient.Database(databaseName).Collection(rule.Collection)

	// 处理提取结果
	switch v := result.(type) {
	case []interface{}:
		// 如果是数组，批量插入
		var documents []interface{}
		for _, item := range v {
			var doc map[string]interface{}
			if itemMap, ok := item.(map[string]interface{}); ok {
				// 如果是对象，复制所有字段
				doc = make(map[string]interface{})
				for key, value := range itemMap {
					doc[key] = value
				}
			} else {
				// 如果不是对象，作为data字段
				doc = map[string]interface{}{"data": item}
			}
			documents = append(documents, doc)
		}
		if len(documents) > 0 {
			_, err = collection.InsertMany(ctx, documents)
		}
	default:
		// 如果是单个对象
		var doc map[string]interface{}
		if itemMap, ok := result.(map[string]interface{}); ok {
			// 如果是对象，复制所有字段
			doc = make(map[string]interface{})
			for key, value := range itemMap {
				doc[key] = value
			}
		} else {
			// 如果不是对象，作为data字段
			doc = map[string]interface{}{"data": result}
		}
		_, err = collection.InsertOne(ctx, doc)
	}

	if err != nil {
		return fmt.Errorf("保存jsonpath结果到MongoDB失败: %w", err)
	}

	return nil
}

// saveJSONPathToMySQL 保存jsonpath解析结果到MySQL
func (p *JSONAPIParser) saveJSONPathToMySQL(ctx context.Context, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	if p.mysqlDB == nil {
		return fmt.Errorf("MySQL连接未初始化")
	}

	// 使用oliveagle/jsonpath提取数据
	result, err := jsonpath.JsonPathLookup(jsonData, rule.JSONPath)
	if err != nil {
		return fmt.Errorf("jsonpath解析失败: %w", err)
	}

	// 处理提取结果
	switch v := result.(type) {
	case []interface{}:
		// 如果是数组，批量插入
		for _, item := range v {
			if err := p.insertJSONObjectToMySQL(ctx, item, rule.TableName); err != nil {
				return err
			}
		}
	default:
		// 如果是单个对象
		if err := p.insertJSONObjectToMySQL(ctx, result, rule.TableName); err != nil {
			return err
		}
	}

	return nil
}

// insertJSONObjectToMySQL 将JSON对象插入到MySQL表中
func (p *JSONAPIParser) insertJSONObjectToMySQL(ctx context.Context, data interface{}, tableName string) error {
	// 将数据转换为JSON字符串，然后插入到表中
	// 这里假设表有一个JSON类型的列来存储数据
	// 实际使用时，可能需要根据具体的表结构来调整
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化JSON数据失败: %w", err)
	}

	// 这里使用一个通用的插入方式，假设表有data列
	// 实际使用时可能需要根据具体的表结构来调整
	query := fmt.Sprintf("INSERT INTO %s (data) VALUES (?)", tableName)
	_, err = p.mysqlDB.ExecContext(ctx, query, string(jsonBytes))

	if err != nil {
		return fmt.Errorf("插入数据到MySQL表 %s 失败: %w", tableName, err)
	}

	return nil
}

// Close 关闭连接
func (p *JSONAPIParser) Close(ctx context.Context) error {
	var err error
	if p.mongoClient != nil {
		if mongoErr := p.mongoClient.Disconnect(ctx); mongoErr != nil {
			err = mongoErr
		}
	}
	if p.mysqlDB != nil {
		if mysqlErr := p.mysqlDB.Close(); mysqlErr != nil {
			if err != nil {
				err = fmt.Errorf("MongoDB关闭错误: %v, MySQL关闭错误: %v", err, mysqlErr)
			} else {
				err = mysqlErr
			}
		}
	}
	return err
}
